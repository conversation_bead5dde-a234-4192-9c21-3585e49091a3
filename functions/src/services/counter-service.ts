import * as admin from "firebase-admin";

export interface CounterEntity {
  id: string;
  value: number;
  updatedAt: admin.firestore.Timestamp;
}

export async function getNextCounterValue(counterName: string) {
  const db = admin.firestore();
  const counterRef = db.collection("counters").doc(counterName);

  try {
    const nextValue = await db.runTransaction(async (transaction) => {
      const counterDoc = await transaction.get(counterRef);

      let currentValue = 0;
      if (counterDoc.exists) {
        const counterData = counterDoc.data() as CounterEntity;
        currentValue = counterData.value;
      }

      const nextValue = currentValue + 1;

      transaction.set(
        counterRef,
        {
          id: counterName,
          value: nextValue,
          updatedAt: admin.firestore.FieldValue.serverTimestamp(),
        },
        { merge: true }
      );

      return nextValue;
    });

    return nextValue;
  } catch (error) {
    console.error(
      `Error getting next counter value for ${counterName}:`,
      error
    );
    throw error;
  }
}
