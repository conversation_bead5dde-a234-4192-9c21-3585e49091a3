/* eslint-disable indent */
import winston from "winston";
import { LoggingWinston } from "@google-cloud/logging-winston";

// Create a Cloud Logging transport
const loggingWinston = new LoggingWinston({
  projectId: process.env.GCLOUD_PROJECT ?? process.env.GCP_PROJECT,
  keyFilename: process.env.GOOGLE_APPLICATION_CREDENTIALS,
  logName: "marketplace-functions",
});

// Create Winston logger
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL ?? "info",
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: "marketplace-functions",
    environment: process.env.APP_ENVIRONMENT ?? "development",
  },
  transports: [
    // Always add console transport for local development
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.simple()
      ),
    }),
  ],
});

// Add Cloud Logging transport only in production or when explicitly enabled
if (
  process.env.NODE_ENV === "production" ||
  process.env.ENABLE_CLOUD_LOGGING === "true"
) {
  logger.add(loggingWinston);
}

// Enhanced logging interface with context support
interface LogContext {
  userId?: string;
  orderId?: string;
  transactionId?: string;
  collectionId?: string;
  operation?: string;
  [key: string]: any;
}

class Logger {
  private readonly winston: winston.Logger;

  constructor(winstonLogger: winston.Logger) {
    this.winston = winstonLogger;
  }

  info(message: string, context?: LogContext) {
    this.winston.info(message, context);
  }

  error(message: string, error?: Error | any, context?: LogContext) {
    const logData = {
      ...context,
      error:
        error instanceof Error
          ? {
              message: error.message,
              stack: error.stack,
              name: error.name,
            }
          : error,
    };
    this.winston.error(message, logData);
  }

  warn(message: string, context?: LogContext) {
    this.winston.warn(message, context);
  }

  debug(message: string, context?: LogContext) {
    this.winston.debug(message, context);
  }

  // Specialized logging methods for common operations
  transactionLog(
    message: string,
    transactionData: {
      transactionId: string;
      amount?: number;
      sender?: string;
      userId?: string;
      userTgId?: string;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: "transaction_processing",
      ...transactionData,
    });
  }

  orderLog(
    message: string,
    orderData: {
      orderId: string;
      userId?: string;
      status?: string;
      price?: number;
      collectionId?: string;
    }
  ) {
    this.info(message, {
      operation: "order_processing",
      ...orderData,
    });
  }

  balanceLog(
    message: string,
    balanceData: {
      userId: string;
      amount?: number;
      operation?: string;
      currentBalance?: number;
      lockedAmount?: number;
      originalAmount?: number;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: "balance_operation",
      ...balanceData,
    });
  }

  feeLog(
    message: string,
    feeData: {
      userId?: string;
      feeAmount: number;
      feeType: string;
      orderId?: string;
    }
  ) {
    this.info(message, {
      operation: "fee_processing",
      ...feeData,
    });
  }

  monitorLog(
    message: string,
    monitorData: {
      monitor: string;
      status?: string;
      count?: number;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: "monitoring",
      ...monitorData,
    });
  }

  botLog(
    message: string,
    botData: {
      userId?: string;
      chatId?: string;
      operation?: string;
      [key: string]: any;
    }
  ) {
    this.info(message, {
      operation: "bot_operation",
      ...botData,
    });
  }
}

// Export singleton logger instance
export const log = new Logger(logger);

// Export types for use in other files
export type { LogContext };
