import { log } from "./utils/logger";

// Simple test function to verify logging works
export function testLogging() {
  console.log("=== Testing New Logging System ===");
  
  // Test basic logging levels
  log.info("Testing info level logging", { 
    operation: "test",
    level: "info"
  });
  
  log.warn("Testing warn level logging", { 
    operation: "test",
    level: "warn"
  });
  
  log.debug("Testing debug level logging", { 
    operation: "test",
    level: "debug"
  });
  
  // Test specialized logging methods
  log.transactionLog("Testing transaction logging", {
    transactionId: "test-tx-123",
    amount: 1.5,
    sender: "test-sender",
    userId: "test-user-456"
  });
  
  log.orderLog("Testing order logging", {
    orderId: "test-order-789",
    userId: "test-user-456",
    status: "ACTIVE",
    price: 2.5,
    collectionId: "test-collection-101"
  });
  
  log.balanceLog("Testing balance logging", {
    userId: "test-user-456",
    amount: 1.5,
    operation: "test_deposit",
    currentBalance: 10.0,
    lockedAmount: 2.0
  });
  
  log.feeLog("Testing fee logging", {
    userId: "test-user-456",
    feeAmount: 0.1,
    feeType: "test_fee",
    orderId: "test-order-789"
  });
  
  log.monitorLog("Testing monitor logging", {
    monitor: "test_monitor",
    status: "running",
    count: 5
  });
  
  log.botLog("Testing bot logging", {
    userId: "test-user-456",
    chatId: "test-chat-123",
    operation: "test_bot_action"
  });
  
  // Test error logging
  try {
    throw new Error("Test error for logging");
  } catch (error) {
    log.error("Testing error logging", error, {
      operation: "test",
      level: "error"
    });
  }
  
  console.log("=== Logging Test Complete ===");
}

// Uncomment to run the test
// testLogging();
