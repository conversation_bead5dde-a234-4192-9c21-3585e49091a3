import * as admin from "firebase-admin";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { TelegramClient, Api } from "telegram";
import { StringSession } from "telegram/sessions/index.js";
import { CollectionEntity, CollectionStatus } from "./types";
import {
  getTelegramBotToken,
  getTelegramApiId,
  getTelegramApiHash,
} from "./config";
import { addDeadlineToOrders as addDeadlineToOrdersService } from "./services/deadline-service";

const db = admin.firestore();

interface LimitedGift {
  id: string;
  limited: boolean;
  upgradeStars: number | null;
}

async function fetchLimitedCollections() {
  const botToken = getTelegramBotToken();
  const apiId = getTelegramApiId();
  const apiHash = getTelegramApiHash();

  const stringSession = new StringSession("");
  const client = new TelegramClient(stringSession, apiId, apiHash, {
    connectionRetries: 5,
  });

  try {
    await client.start({
      botAuthToken: botToken,
    });

    const result = await client.invoke(
      new Api.payments.GetStarGifts({ hash: 0 })
    );

    // Handle the result properly based on its type
    if (!("gifts" in result)) {
      console.log("No gifts found in result");
      return [];
    }

    const limitedGifts = (result as any).gifts
      .filter((gift: any) => gift.limited === true)
      .map((gift: any) => ({
        id: gift.id.toString(),
        limited: gift.limited,
        upgradeStars: gift.upgradeStars ? gift.upgradeStars.toString() : null,
      }));

    console.log(`Found ${limitedGifts.length} limited gifts from Telegram API`);
    return limitedGifts;
  } catch (error) {
    console.error("Error fetching limited collections from Telegram:", error);
    throw error;
  } finally {
    await client.disconnect();
  }
}

async function updateCollectionToMarket(collectionId: string) {
  const collectionRef = db.collection("collections").doc(collectionId);

  await collectionRef.update({
    status: CollectionStatus.MARKET,
    launchedAt: admin.firestore.Timestamp.now(),
  });

  console.log(`Updated collection ${collectionId} to MARKET status`);
}

async function addDeadlineToOrders(collectionId: string) {
  await addDeadlineToOrdersService(db, collectionId);
}

async function createNewCollection(gift: LimitedGift) {
  const newCollection: CollectionEntity = {
    id: gift.id,
    name: "Limited Gift",
    description: "Limited collection",
    status: CollectionStatus.PREMARKET,
    floorPrice: 0.1, // Default floor price in TON
    active: true, // New collections are active by default
  };

  await db.collection("collections").doc(gift.id).set(newCollection);
  console.log(`Created new collection ${gift.id} in Firestore`);
}

async function processUpgradeableCollection(gift: LimitedGift) {
  try {
    const collectionRef = db.collection("collections").doc(gift.id);
    const collectionDoc = await collectionRef.get();

    if (!collectionDoc.exists) {
      console.log(
        `Collection ${gift.id} not found in Firestore, creating new collection`
      );
      await createNewCollection(gift);

      // After creating, we need to get the collection data to continue processing
      const newCollectionDoc = await collectionRef.get();
      if (!newCollectionDoc.exists) {
        console.error(`Failed to create collection ${gift.id}`);
        return;
      }
    }

    // Get the collection data (either existing or newly created)
    const updatedCollectionDoc = await collectionRef.get();
    const collection = updatedCollectionDoc.data() as CollectionEntity;

    // Skip if not PREMARKET status
    if (collection.status !== CollectionStatus.PREMARKET) {
      console.log(
        `Collection ${gift.id} status is ${collection.status}, skipping`
      );
      return;
    }

    console.log(
      `Processing collection ${gift.id} with upgradeStars: ${gift.upgradeStars}`
    );

    // Update collection status to MARKET and set launchedAt
    await updateCollectionToMarket(gift.id);

    // Add deadlines to orders without them
    await addDeadlineToOrders(gift.id);

    console.log(`Successfully processed collection ${gift.id}`);
  } catch (error) {
    console.error(`Error processing collection ${gift.id}:`, error);
    throw error;
  }
}

async function ensureCollectionExists(gift: LimitedGift) {
  try {
    const collectionRef = db.collection("collections").doc(gift.id);
    const collectionDoc = await collectionRef.get();

    if (!collectionDoc.exists) {
      console.log(
        `Collection ${gift.id} not found in Firestore, creating new collection`
      );
      await createNewCollection(gift);
    } else {
      console.log(`Collection ${gift.id} already exists in Firestore`);
    }
  } catch (error) {
    console.error(`Error ensuring collection ${gift.id} exists:`, error);
    throw error;
  }
}

export async function checkLimitedCollections() {
  try {
    console.log("Starting limited collections check...");

    const limitedGifts = await fetchLimitedCollections();

    if (limitedGifts.length === 0) {
      console.log("No limited collections found from Telegram API");
      return;
    }

    console.log(
      `Found ${limitedGifts.length} limited collections from Telegram API`
    );

    // First, ensure all limited collections exist in Firestore
    for (const gift of limitedGifts) {
      try {
        await ensureCollectionExists(gift);
      } catch (error) {
        console.error(`Failed to ensure collection ${gift.id} exists:`, error);
        // Continue processing other collections even if one fails
      }
    }

    // Then, process upgradeable collections (those with upgradeStars)
    const upgradeableGifts = limitedGifts.filter(
      // @ts-expect-error: upgradeStars is not null
      (gift) => gift.upgradeStars !== null
    );

    if (upgradeableGifts.length === 0) {
      console.log("No upgradeable limited collections found");
      return;
    }

    console.log(
      `Found ${upgradeableGifts.length} upgradeable limited collections`
    );

    for (const gift of upgradeableGifts) {
      try {
        await processUpgradeableCollection(gift);
      } catch (error) {
        console.error(`Failed to process collection ${gift.id}:`, error);
        // Continue processing other collections even if one fails
      }
    }

    console.log("Limited collections check completed");
  } catch (error) {
    console.error("Error in checkLimitedCollections:", error);
    throw error;
  }
}

export const limitedCollectionsMonitor = onSchedule(
  {
    schedule: "0 1 * * *", // Run daily at 1 AM UTC
    timeZone: "UTC",
  },
  async () => {
    try {
      console.log(
        "Limited collections monitor triggered at:",
        new Date().toISOString()
      );
      await checkLimitedCollections();
      console.log("Limited collections monitor completed successfully");
    } catch (error) {
      console.error("Limited collections monitor failed:", error);
    }
  }
);
