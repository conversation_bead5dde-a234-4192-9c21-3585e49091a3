# Logging Migration to @google-cloud/logging-winston

## Overview
Successfully migrated from console.log to structured logging using `@google-cloud/logging-winston` for better log readability in Google Cloud Console.

## What Was Changed

### 1. Dependencies Added
- `@google-cloud/logging-winston`
- `winston`

### 2. New Logger Utility
Created `functions/src/utils/logger.ts` with:
- Centralized Winston logger configuration
- Cloud Logging integration for production
- Console logging for development
- Specialized logging methods for different operations

### 3. Specialized Logging Methods
- `log.info()` - General information
- `log.error()` - Error logging with stack traces
- `log.warn()` - Warning messages
- `log.debug()` - Debug information
- `log.transactionLog()` - TON transaction processing
- `log.orderLog()` - Order operations
- `log.balanceLog()` - Balance operations
- `log.feeLog()` - Fee processing
- `log.monitorLog()` - Monitoring operations
- `log.botLog()` - Bot operations

### 4. Files Updated
- `functions/src/ton-monitor.ts` - Complete migration
- `functions/src/index.ts` - Complete migration
- `functions/src/services/fee-service.ts` - Partial migration (key functions)
- `functions/src/services/balance-service.ts` - Complete migration
- `functions/src/bot-health-check.ts` - Complete migration
- `functions/src/limited-collections-monitor.ts` - Partial migration
- `functions/src/utils.ts` - Complete migration
- `functions/src/order-functions/buyer-order-functions.ts` - Error logging
- `functions/src/order-functions/seller-order-functions.ts` - Error logging

## Configuration

### Environment Variables
The logger automatically detects the environment:
- **Production**: Enables Cloud Logging + Console
- **Development**: Console only
- **Force Cloud Logging**: Set `ENABLE_CLOUD_LOGGING=true`

### Log Levels
- Default: `info`
- Override with: `LOG_LEVEL=debug|info|warn|error`

## Structured Logging Format

All logs now include structured context:
```typescript
log.transactionLog("Processing transaction", {
  transactionId: "123",
  amount: 1.5,
  sender: "wallet-address",
  userId: "user-456"
});
```

## Benefits

1. **Better Readability**: Structured logs in Google Cloud Console
2. **Searchable**: Filter by operation, userId, transactionId, etc.
3. **Contextual**: Rich metadata for debugging
4. **Consistent**: Standardized logging across the codebase
5. **Environment Aware**: Different behavior for dev/prod

## Testing

Run the test function:
```typescript
import { testLogging } from './test-logging';
testLogging();
```

## Remaining Work

Some files still have console.log statements that need migration:
- Complete migration of `fee-service.ts` (many console.log statements remain)
- Complete migration of `limited-collections-monitor.ts`
- Other service files in `services/` directory
- Any remaining console.log in other files

## Usage Examples

```typescript
import { log } from './utils/logger';

// Basic logging
log.info("User authenticated", { userId: "123", operation: "auth" });

// Error logging
log.error("Database connection failed", error, { operation: "db_connect" });

// Specialized logging
log.transactionLog("Transaction processed", {
  transactionId: "tx-123",
  amount: 1.5,
  userId: "user-456"
});
```

## Migration Pattern

Replace:
```typescript
console.log("Processing transaction:", txId, amount);
```

With:
```typescript
log.transactionLog("Processing transaction", {
  transactionId: txId,
  amount: amount
});
```
